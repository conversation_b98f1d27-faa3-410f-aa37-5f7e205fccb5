<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Loading Test - DocForge AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .font-preview {
            font-size: 24px;
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .loading { background: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Font Loading Test - DocForge AI</h1>
    
    <div class="test-section">
        <h2>1. Font Loader Utility Test</h2>
        <div id="fontLoaderStatus" class="status loading">Loading font loader...</div>
        <button onclick="testSystemFont()">Test System Font (Arial)</button>
        <button onclick="testGoogleFont()">Test Google Font (Roboto)</button>
        <button onclick="testCustomFont()">Test Custom Font (Playfair Display)</button>
        <div id="fontResults"></div>
    </div>

    <div class="test-section">
        <h2>2. Canvas Rendering Test</h2>
        <div id="canvasStatus" class="status loading">Preparing canvas test...</div>
        <button onclick="testCanvasRendering()">Test Canvas Font Rendering</button>
        <canvas id="testCanvas" width="600" height="200"></canvas>
        <div id="canvasResults"></div>
    </div>

    <div class="test-section">
        <h2>3. Template Preview Test</h2>
        <div id="templateStatus" class="status loading">Preparing template test...</div>
        <button onclick="testTemplatePreview()">Test Template with Custom Fonts</button>
        <div id="templateResults"></div>
        <canvas id="templateCanvas" width="400" height="300"></canvas>
    </div>

    <script type="module">
        // Import the font loader
        import fontLoader from '/src/utils/fontLoader.js';
        import imageOverlayService from '/src/services/imageOverlayService.js';

        // Make utilities available globally for testing
        window.fontLoader = fontLoader;
        window.imageOverlayService = imageOverlayService;

        // Update status
        document.getElementById('fontLoaderStatus').textContent = 'Font loader ready!';
        document.getElementById('fontLoaderStatus').className = 'status success';
        document.getElementById('canvasStatus').textContent = 'Canvas test ready!';
        document.getElementById('canvasStatus').className = 'status success';
        document.getElementById('templateStatus').textContent = 'Template test ready!';
        document.getElementById('templateStatus').className = 'status success';

        // Test functions
        window.testSystemFont = async function() {
            const results = document.getElementById('fontResults');
            results.innerHTML = '<div class="status loading">Testing system font...</div>';
            
            try {
                const result = await fontLoader.ensureFontLoaded('Arial');
                results.innerHTML = `
                    <div class="status success">✅ System font test passed: ${result}</div>
                    <div class="font-preview" style="font-family: Arial;">
                        The quick brown fox jumps over the lazy dog (Arial)
                    </div>
                `;
            } catch (error) {
                results.innerHTML = `<div class="status error">❌ System font test failed: ${error.message}</div>`;
            }
        };

        window.testGoogleFont = async function() {
            const results = document.getElementById('fontResults');
            results.innerHTML = '<div class="status loading">Loading Google font...</div>';
            
            try {
                const result = await fontLoader.ensureFontLoaded('Roboto');
                results.innerHTML = `
                    <div class="status success">✅ Google font test passed: ${result}</div>
                    <div class="font-preview" style="font-family: Roboto, Arial, sans-serif;">
                        The quick brown fox jumps over the lazy dog (Roboto)
                    </div>
                `;
            } catch (error) {
                results.innerHTML = `<div class="status error">❌ Google font test failed: ${error.message}</div>`;
            }
        };

        window.testCustomFont = async function() {
            const results = document.getElementById('fontResults');
            results.innerHTML = '<div class="status loading">Loading custom font...</div>';
            
            try {
                const result = await fontLoader.ensureFontLoaded('Playfair Display');
                results.innerHTML = `
                    <div class="status success">✅ Custom font test passed: ${result}</div>
                    <div class="font-preview" style="font-family: 'Playfair Display', Georgia, serif;">
                        The quick brown fox jumps over the lazy dog (Playfair Display)
                    </div>
                `;
            } catch (error) {
                results.innerHTML = `<div class="status error">❌ Custom font test failed: ${error.message}</div>`;
            }
        };

        window.testCanvasRendering = async function() {
            const results = document.getElementById('canvasResults');
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            results.innerHTML = '<div class="status loading">Testing canvas font rendering...</div>';
            
            try {
                // Clear canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // Test different fonts
                const fonts = [
                    { name: 'Arial', y: 40 },
                    { name: 'Roboto', y: 80 },
                    { name: 'Playfair Display', y: 120 },
                    { name: 'Montserrat', y: 160 }
                ];
                
                for (const font of fonts) {
                    // Load font first
                    await fontLoader.ensureFontLoaded(font.name);
                    
                    // Apply font to canvas
                    const fontWithFallbacks = fontLoader.getFontWithFallbacks(font.name);
                    ctx.font = `24px ${fontWithFallbacks}`;
                    ctx.fillStyle = '#333';
                    ctx.fillText(`${font.name}: The quick brown fox jumps`, 20, font.y);
                }
                
                results.innerHTML = '<div class="status success">✅ Canvas font rendering test completed!</div>';
            } catch (error) {
                results.innerHTML = `<div class="status error">❌ Canvas test failed: ${error.message}</div>`;
            }
        };

        window.testTemplatePreview = async function() {
            const results = document.getElementById('templateResults');
            const canvas = document.getElementById('templateCanvas');
            
            results.innerHTML = '<div class="status loading">Testing template preview with custom fonts...</div>';
            
            try {
                // Mock template with custom fonts
                const mockTemplate = {
                    id: 'test-template',
                    background_image_width: 400,
                    background_image_height: 300,
                    background_image_url: 'data:image/svg+xml;base64,' + btoa(`
                        <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                            <rect width="400" height="300" fill="#e3f2fd"/>
                            <text x="200" y="150" text-anchor="middle" fill="#1976d2" font-size="16">Background</text>
                        </svg>
                    `),
                    text_overlays: {
                        overlays: [
                            {
                                id: 'title',
                                placeholder: '{{title}}',
                                position: { x: 50, y: 50, width: 300, height: 60 },
                                styling: {
                                    fontSize: 28,
                                    fontFamily: 'Playfair Display',
                                    fontWeight: 'bold',
                                    color: '#1a1a1a',
                                    textAlign: 'center'
                                }
                            },
                            {
                                id: 'subtitle',
                                placeholder: '{{subtitle}}',
                                position: { x: 50, y: 120, width: 300, height: 40 },
                                styling: {
                                    fontSize: 18,
                                    fontFamily: 'Roboto',
                                    fontWeight: 'normal',
                                    color: '#666666',
                                    textAlign: 'center'
                                }
                            }
                        ]
                    }
                };

                const documentData = {
                    title: 'Custom Font Test',
                    subtitle: 'Testing font loading in templates'
                };

                // Render template
                const renderedCanvas = await imageOverlayService.renderTemplate(mockTemplate, documentData);
                
                // Copy to display canvas
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(renderedCanvas, 0, 0);
                
                results.innerHTML = '<div class="status success">✅ Template preview test completed! Check the canvas above.</div>';
            } catch (error) {
                results.innerHTML = `<div class="status error">❌ Template test failed: ${error.message}</div>`;
                console.error('Template test error:', error);
            }
        };
    </script>
</body>
</html>
